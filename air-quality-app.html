<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 390px;
            height: 844px;
            background: #ffffff;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            width: 375px;
            height: 44px;
            top: 0;
            left: 11px;
        }

        /* 主要内容区域 */
        .main-content {
            position: absolute;
            width: 393px;
            height: 783px;
            top: 69px;
            left: 0;
            background: #ffffff;
        }

        /* 1. 上半部分 */
        .top-section {
            position: relative;
            height: 320px;
            margin-bottom: 30px;
            padding-top: 20px;
        }

        /* 圆形装饰元素 */
        .circular-container {
            position: absolute;
            width: 100%;
            top: 0;
            left: 0;
        }

        .outer-ellipse {
            position: absolute;
            width: 311px;
            height: 311px;
            top: 16px;
            left: 40px;
            border-radius: 50%;
            aspect-ratio: 1;
            object-fit: cover;
        }

        .middle-ellipse {
            position: absolute;
            width: 231px;
            height: 231px;
            top: 56px;
            left: 80px;
            border-radius: 50%;
            aspect-ratio: 1;
            object-fit: cover;
        }

        .inner-ellipse {
            position: absolute;
            width: 175px;
            height: 175px;
            top: 84px;
            left: 108px;
            border-radius: 50%;
            aspect-ratio: 1;
            object-fit: cover;
        }

        /* 空气质量显示 */
        .air-quality-display {
            position: absolute;
            width: 75px;
            height: 96px;
            top: 49px;
            left: 166px;
            z-index: 10;
        }

        .air-quality-label {
            position: absolute;
            top: 0;
            left: 4px;
            color: #454545;
            font-size: 20px;
            white-space: nowrap;
            font-weight: normal;
            font-family: 'HarmonyOS_Sans-Regular', Helvetica;
        }

        .air-quality-value {
            position: absolute;
            top: 30px;
            left: 0;
            color: #454545;
            font-size: 48px;
            font-weight: bold;
            font-family: 'HarmonyOS_Sans-Regular', Helvetica;
        }

        .air-quality-unit {
            position: absolute;
            top: 80px;
            left: 0;
            color: #909090;
            font-size: 10px;
            white-space: nowrap;
            font-family: 'HarmonyOS_Sans-Regular', Helvetica;
        }

        /* 汽车图片 */
        .car-image {
            position: absolute;
            width: 315px;
            height: 210px;
            top: 69px;
            left: 39px;
            object-fit: cover;
        }

        /* 守护天数 */
        .protection-days {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #666;
        }

        .protection-text {
            font-size: 15px;
            margin-bottom: 6px;
            font-weight: 400;
        }

        .protection-status {
            font-size: 13px;
            color: #f97316;
            font-weight: 500;
        }

        /* 2. 数据指标行 */
        .metrics-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .metric-item {
            text-align: center;
            color: #333;
            flex: 1;
        }

        .metric-value {
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 8px;
            line-height: 1;
        }

        .metric-label {
            font-size: 12px;
            color: #666;
            line-height: 1.2;
        }

        /* 3. 天气卡片 */
        .weather-card {
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            margin: 0 20px 20px 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        /* 刷新按钮 */
        .refresh-btn {
            position: absolute;
            top: 12px;
            right: 16px;
            background: none;
            border: none;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .refresh-btn:hover {
            background: #f5f5f5;
        }

        .weather-main {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        .weather-left {
            flex: 1;
        }

        .city-name {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }

        .temperature {
            font-size: 52px;
            font-weight: 900;
            color: #333;
            line-height: 0.9;
        }

        .weather-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .weather-info {
            text-align: right;
        }

        .weather-detail {
            font-size: 11px;
            color: #666;
            margin-bottom: 2px;
            line-height: 1.3;
        }

        .weather-data {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .weather-data-item {
            text-align: center;
            flex: 1;
        }

        .data-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .data-value {
            font-size: 32px;
            font-weight: 900;
            line-height: 1;
        }

        .data-value.orange {
            color: #f97316;
        }

        .data-value.green {
            color: #10b981;
        }

        .data-value.dark-green {
            color: #15803d;
        }

        /* 4. 设备控制面板 */
        .device-control-panel {
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            margin: 0 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .control-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .device-status {
            text-align: center;
            flex: 1;
        }

        .status-badge {
            background: #10b981;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 6px;
            display: inline-block;
        }

        .status-text {
            font-size: 11px;
            color: #666;
        }

        .power-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #f97316;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 15px;
            border: none;
        }

        .power-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(249, 115, 22, 0.3);
        }

        .power-icon {
            font-size: 28px;
            color: white;
        }

        .speed-control {
            text-align: center;
            flex: 1;
        }

        .speed-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .speed-value {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <img
            class="status-bar"
            alt="Status bar"
            src="https://c.animaapp.com/mdqqarhondzElL/img/---------.svg"
        />

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 1. 上半部分：彩色圆弧 + 空气质量 + 汽车 + 守护天数 -->
            <div class="top-section">
                <!-- 圆形渐变装饰元素 -->
                <div class="circular-container">
                    <img
                        class="outer-ellipse"
                        alt="Outer ellipse"
                        src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-11.svg"
                    />
                    <img
                        class="middle-ellipse"
                        alt="Middle ellipse"
                        src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-12.svg"
                    />
                    <img
                        class="inner-ellipse"
                        alt="Inner ellipse"
                        src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-13.svg"
                    />
                </div>

                <!-- 空气质量中心指示器 -->
                <div class="air-quality-display">
                    <div class="air-quality-label">空气优</div>
                    <div class="air-quality-value">25</div>
                    <div class="air-quality-unit">车内综合空气质量</div>
                </div>

                <!-- 汽车可视化 -->
                <img
                    class="car-image"
                    alt="Car visualization"
                    src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
                />

                <!-- 守护天数 -->
                <div class="protection-days">
                    <div class="protection-text">已为您健康守护 231 天</div>
                    <div class="protection-status">设备 清洁健康</div>
                </div>
            </div>

            <!-- 2. 数据指标行 -->
            <div class="metrics-row">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 3. 天气卡片 -->
            <div class="weather-card">
                <button class="refresh-btn" onclick="refreshWeather()">刷新</button>
                <div class="weather-main">
                    <div class="weather-left">
                        <div class="city-name">深圳市</div>
                        <div class="temperature">33°C</div>
                    </div>
                    <div class="weather-right">
                        <div class="weather-info">
                            <div class="weather-detail">空气质量：优</div>
                            <div class="weather-detail">湿度：52%</div>
                            <div class="weather-detail">能见度：30km</div>
                        </div>
                    </div>
                </div>
                
                <div class="weather-data">
                    <div class="weather-data-item">
                        <div class="data-label">车外PM2.5</div>
                        <div class="data-value orange">105</div>
                    </div>
                    <div class="weather-data-item">
                        <div class="data-label">车内PM2.5</div>
                        <div class="data-value green">14</div>
                    </div>
                    <div class="weather-data-item">
                        <div class="data-label">车内负氧离子</div>
                        <div class="data-value dark-green">628</div>
                    </div>
                </div>
            </div>

            <!-- 4. 设备控制面板 -->
            <div class="device-control-panel">
                <div class="control-container">
                    <div class="device-status">
                        <div class="status-badge">设备运转</div>
                        <div class="status-text">正常</div>
                    </div>

                    <button class="power-button" onclick="togglePower()">
                        <div class="power-icon">⏻</div>
                    </button>

                    <div class="speed-control">
                        <div class="speed-label">档位</div>
                        <div class="speed-value" id="speedValue">低</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = true;
        let currentSpeed = 0; // 0: 低, 1: 中, 2: 高
        const speedLevels = ['低', '中', '高'];

        function togglePower() {
            isRunning = !isRunning;
            const statusBadge = document.querySelector('.status-badge');
            const powerButton = document.querySelector('.power-button');

            if (isRunning) {
                statusBadge.textContent = '设备运转';
                statusBadge.style.background = '#10b981';
                powerButton.style.background = '#f97316';
            } else {
                statusBadge.textContent = '已停止';
                statusBadge.style.background = '#6b7280';
                powerButton.style.background = '#6b7280';
            }
        }

        function refreshWeather() {
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.textContent = '刷新中...';
            refreshBtn.disabled = true;

            // 模拟刷新延迟
            setTimeout(() => {
                refreshBtn.textContent = '刷新';
                refreshBtn.disabled = false;

                // 这里可以添加实际的天气数据更新逻辑
                console.log('天气数据已刷新');
            }, 1000);
        }

        // 点击档位切换
        document.addEventListener('DOMContentLoaded', function() {
            const speedControl = document.querySelector('.speed-control');
            speedControl.addEventListener('click', function() {
                if (isRunning) {
                    currentSpeed = (currentSpeed + 1) % 3;
                    document.getElementById('speedValue').textContent = speedLevels[currentSpeed];
                }
            });
        });
    </script>
</body>
</html>
