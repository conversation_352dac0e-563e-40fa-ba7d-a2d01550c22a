<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 390px;
            height: 844px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-weight: 600;
            z-index: 100;
        }

        .time {
            font-size: 17px;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px;
        }

        /* 1. 上半部分 */
        .top-section {
            position: relative;
            height: 300px;
            margin-bottom: 30px;
        }

        /* 彩色圆弧 */
        .arc-background {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 空气质量显示 */
        .air-quality-display {
            position: absolute;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: white;
        }

        .air-quality-label {
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .air-quality-value {
            font-size: 64px;
            font-weight: bold;
            margin-bottom: 5px;
            line-height: 1;
        }

        .air-quality-unit {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 汽车图片 */
        .car-image {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 220px;
            height: 110px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 220 110"><rect width="220" height="110" fill="%23rgba(255,255,255,0.2)" rx="20"/><text x="110" y="60" text-anchor="middle" fill="white" font-size="16">🚗 汽车图片</text></svg>') center/contain no-repeat;
        }

        /* 守护天数 */
        .protection-days {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: white;
        }

        .protection-text {
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .protection-status {
            font-size: 14px;
            opacity: 0.8;
            color: #fbbf24;
        }

        /* 2. 数据指标行 */
        .metrics-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
            padding: 0 10px;
        }

        .metric-item {
            text-align: center;
            color: white;
            flex: 1;
        }

        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            line-height: 1;
        }

        .metric-label {
            font-size: 12px;
            opacity: 0.9;
            line-height: 1.2;
        }

        /* 3. 天气卡片 */
        .weather-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .weather-main {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        .weather-left {
            flex: 1;
        }

        .city-name {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .temperature {
            font-size: 42px;
            font-weight: bold;
            color: #333;
            line-height: 1;
        }

        .weather-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
        }

        .weather-icon {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .weather-info {
            text-align: right;
        }

        .weather-detail {
            font-size: 13px;
            color: #666;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .weather-data {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .weather-data-item {
            text-align: center;
            flex: 1;
        }

        .data-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .data-value {
            font-size: 24px;
            font-weight: bold;
            line-height: 1;
        }

        .data-value.orange {
            color: #f97316;
        }

        .data-value.green {
            color: #10b981;
        }

        .data-value.dark-green {
            color: #15803d;
        }

        /* 4. 设备控制面板 */
        .device-control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .control-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .device-status {
            text-align: center;
            flex: 1;
        }

        .status-badge {
            background: #10b981;
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            display: inline-block;
        }

        .status-text {
            font-size: 12px;
            color: #666;
        }

        .power-button {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #f97316;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 20px;
        }

        .power-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 20px rgba(249, 115, 22, 0.4);
        }

        .power-icon {
            font-size: 32px;
            color: white;
        }

        .speed-control {
            text-align: center;
            flex: 1;
        }

        .speed-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }

        .speed-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .speed-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: 2px solid #ddd;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 20px;
            color: #666;
            font-weight: bold;
            transition: all 0.2s ease;
        }

        .speed-btn:hover {
            border-color: #f97316;
            color: #f97316;
        }

        .speed-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            min-width: 30px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time">9:41</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 1. 上半部分：彩色圆弧 + 空气质量 + 汽车 + 守护天数 -->
            <div class="top-section">
                <!-- 彩色圆弧背景 -->
                <div class="arc-background">
                    <svg width="350" height="180" viewBox="0 0 350 180">
                        <defs>
                            <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
                                <stop offset="25%" style="stop-color:#22d3ee;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#fbbf24;stop-opacity:1" />
                                <stop offset="75%" style="stop-color:#f97316;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <path d="M 50 140 A 125 125 0 0 1 300 140" 
                              stroke="url(#arcGradient)" 
                              stroke-width="12" 
                              fill="none" 
                              stroke-linecap="round"/>
                    </svg>
                </div>

                <!-- 空气质量显示 -->
                <div class="air-quality-display">
                    <div class="air-quality-label">空气优</div>
                    <div class="air-quality-value">25</div>
                    <div class="air-quality-unit">空气质量</div>
                </div>

                <!-- 汽车图片 -->
                <div class="car-image"></div>

                <!-- 守护天数 -->
                <div class="protection-days">
                    <div class="protection-text">已为您健康守护 231 天</div>
                    <div class="protection-status">设备 清洁健康</div>
                </div>
            </div>

            <!-- 2. 数据指标行 -->
            <div class="metrics-row">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 3. 天气卡片 -->
            <div class="weather-card">
                <div class="weather-main">
                    <div class="weather-left">
                        <div class="city-name">深圳市</div>
                        <div class="temperature">33°C</div>
                    </div>
                    <div class="weather-right">
                        <div class="weather-icon">☀️</div>
                        <div class="weather-info">
                            <div class="weather-detail">空气质量：优</div>
                            <div class="weather-detail">湿度：52%</div>
                            <div class="weather-detail">能见度：30km</div>
                        </div>
                    </div>
                </div>
                
                <div class="weather-data">
                    <div class="weather-data-item">
                        <div class="data-label">车外PM2.5</div>
                        <div class="data-value orange">105</div>
                    </div>
                    <div class="weather-data-item">
                        <div class="data-label">车内PM2.5</div>
                        <div class="data-value green">14</div>
                    </div>
                    <div class="weather-data-item">
                        <div class="data-label">车内负氧离子</div>
                        <div class="data-value dark-green">628</div>
                    </div>
                </div>
            </div>

            <!-- 4. 设备控制面板 -->
            <div class="device-control-panel">
                <div class="control-container">
                    <div class="device-status">
                        <div class="status-badge">运行中</div>
                        <div class="status-text">正常</div>
                    </div>
                    
                    <div class="power-button" onclick="togglePower()">
                        <div class="power-icon">⏻</div>
                    </div>
                    
                    <div class="speed-control">
                        <div class="speed-label">档位</div>
                        <div class="speed-buttons">
                            <button class="speed-btn" onclick="decreaseSpeed()">-</button>
                            <span class="speed-value" id="speedValue">低</span>
                            <button class="speed-btn" onclick="increaseSpeed()">+</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = true;
        let currentSpeed = 0; // 0: 低, 1: 中, 2: 高
        const speedLevels = ['低', '中', '高'];

        function togglePower() {
            isRunning = !isRunning;
            const statusBadge = document.querySelector('.status-badge');
            const powerButton = document.querySelector('.power-button');
            
            if (isRunning) {
                statusBadge.textContent = '运行中';
                statusBadge.style.background = '#10b981';
                powerButton.style.background = '#f97316';
            } else {
                statusBadge.textContent = '已停止';
                statusBadge.style.background = '#6b7280';
                powerButton.style.background = '#6b7280';
            }
        }

        function increaseSpeed() {
            if (currentSpeed < 2) {
                currentSpeed++;
                updateSpeedDisplay();
            }
        }

        function decreaseSpeed() {
            if (currentSpeed > 0) {
                currentSpeed--;
                updateSpeedDisplay();
            }
        }

        function updateSpeedDisplay() {
            document.getElementById('speedValue').textContent = speedLevels[currentSpeed];
        }
    </script>
</body>
</html>
