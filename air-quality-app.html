<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 390px;
            height: 844px;
            background: #f8f9fa;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #000;
            font-weight: 600;
            z-index: 100;
        }

        .time {
            font-size: 17px;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px;
        }

        /* 1. 上半部分 */
        .top-section {
            position: relative;
            height: 350px;
            margin-bottom: 20px;
        }

        /* 彩色圆弧 */
        .arc-background {
            position: absolute;
            top: 40px;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 空气质量显示 */
        .air-quality-display {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #333;
            z-index: 10;
        }

        .air-quality-label {
            font-size: 14px;
            margin-bottom: 5px;
            font-weight: 400;
            color: #666;
        }

        .air-quality-value {
            font-size: 72px;
            font-weight: bold;
            margin-bottom: 0;
            line-height: 1;
            color: #333;
        }

        .air-quality-unit {
            font-size: 12px;
            color: #666;
        }

        /* 汽车图片 */
        .car-image {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            width: 280px;
            height: 140px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 280 140"><defs><linearGradient id="carGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%2340e0d0;stop-opacity:1" /><stop offset="100%" style="stop-color:%2300d4aa;stop-opacity:1" /></linearGradient></defs><ellipse cx="140" cy="120" rx="120" ry="15" fill="rgba(0,0,0,0.1)"/><path d="M40 80 Q40 60 60 60 L220 60 Q240 60 240 80 L240 100 Q240 110 230 110 L210 110 Q200 120 180 120 Q160 120 150 110 L130 110 Q120 120 100 120 Q80 120 70 110 L50 110 Q40 110 40 100 Z" fill="url(%23carGradient)"/><circle cx="100" cy="110" r="18" fill="%23333"/><circle cx="100" cy="110" r="12" fill="%23666"/><circle cx="180" cy="110" r="18" fill="%23333"/><circle cx="180" cy="110" r="12" fill="%23666"/><rect x="80" y="70" width="120" height="25" rx="5" fill="rgba(255,255,255,0.3)"/></svg>') center/contain no-repeat;
        }

        /* 守护天数 */
        .protection-days {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #666;
        }

        .protection-text {
            font-size: 14px;
            margin-bottom: 4px;
            font-weight: 400;
        }

        .protection-status {
            font-size: 12px;
            color: #f97316;
        }

        /* 2. 数据指标行 */
        .metrics-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .metric-item {
            text-align: center;
            color: #333;
            flex: 1;
        }

        .metric-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 6px;
            line-height: 1;
        }

        .metric-label {
            font-size: 11px;
            color: #666;
            line-height: 1.2;
        }

        /* 3. 天气卡片 */
        .weather-card {
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            margin: 0 20px 20px 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        /* 刷新按钮 */
        .refresh-btn {
            position: absolute;
            top: 12px;
            right: 16px;
            background: none;
            border: none;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .refresh-btn:hover {
            background: #f5f5f5;
        }

        .weather-main {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        .weather-left {
            flex: 1;
        }

        .city-name {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .temperature {
            font-size: 48px;
            font-weight: bold;
            color: #333;
            line-height: 1;
        }

        .weather-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .weather-info {
            text-align: right;
        }

        .weather-detail {
            font-size: 11px;
            color: #666;
            margin-bottom: 2px;
            line-height: 1.3;
        }

        .weather-data {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .weather-data-item {
            text-align: center;
            flex: 1;
        }

        .data-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .data-value {
            font-size: 24px;
            font-weight: bold;
            line-height: 1;
        }

        .data-value.orange {
            color: #f97316;
        }

        .data-value.green {
            color: #10b981;
        }

        .data-value.dark-green {
            color: #15803d;
        }

        /* 4. 设备控制面板 */
        .device-control-panel {
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            margin: 0 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .control-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .device-status {
            text-align: center;
            flex: 1;
        }

        .status-badge {
            background: #10b981;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
            display: inline-block;
        }

        .status-text {
            font-size: 11px;
            color: #666;
        }

        .power-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #f97316;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 15px;
            border: none;
        }

        .power-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(249, 115, 22, 0.3);
        }

        .power-icon {
            font-size: 28px;
            color: white;
        }

        .speed-control {
            text-align: center;
            flex: 1;
        }

        .speed-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .speed-value {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time">9:41</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 1. 上半部分：彩色圆弧 + 空气质量 + 汽车 + 守护天数 -->
            <div class="top-section">
                <!-- 彩色圆弧背景 -->
                <div class="arc-background">
                    <svg width="320" height="180" viewBox="0 0 320 180">
                        <defs>
                            <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
                                <stop offset="30%" style="stop-color:#f97316;stop-opacity:1" />
                                <stop offset="60%" style="stop-color:#22d3ee;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <path d="M 40 160 A 120 120 0 0 1 280 160"
                              stroke="url(#arcGradient)"
                              stroke-width="16"
                              fill="none"
                              stroke-linecap="round"/>
                    </svg>
                </div>

                <!-- 空气质量显示 -->
                <div class="air-quality-display">
                    <div class="air-quality-label">空气优</div>
                    <div class="air-quality-value">25</div>
                    <div class="air-quality-unit">空气质量</div>
                </div>

                <!-- 汽车图片 -->
                <div class="car-image"></div>

                <!-- 守护天数 -->
                <div class="protection-days">
                    <div class="protection-text">已为您健康守护 231 天</div>
                    <div class="protection-status">设备 清洁健康</div>
                </div>
            </div>

            <!-- 2. 数据指标行 -->
            <div class="metrics-row">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 3. 天气卡片 -->
            <div class="weather-card">
                <button class="refresh-btn" onclick="refreshWeather()">刷新</button>
                <div class="weather-main">
                    <div class="weather-left">
                        <div class="city-name">深圳市</div>
                        <div class="temperature">33°C</div>
                    </div>
                    <div class="weather-right">
                        <div class="weather-info">
                            <div class="weather-detail">空气质量：优</div>
                            <div class="weather-detail">湿度：52%</div>
                            <div class="weather-detail">能见度：30km</div>
                        </div>
                    </div>
                </div>
                
                <div class="weather-data">
                    <div class="weather-data-item">
                        <div class="data-label">车外PM2.5</div>
                        <div class="data-value orange">105</div>
                    </div>
                    <div class="weather-data-item">
                        <div class="data-label">车内PM2.5</div>
                        <div class="data-value green">14</div>
                    </div>
                    <div class="weather-data-item">
                        <div class="data-label">车内负氧离子</div>
                        <div class="data-value dark-green">628</div>
                    </div>
                </div>
            </div>

            <!-- 4. 设备控制面板 -->
            <div class="device-control-panel">
                <div class="control-container">
                    <div class="device-status">
                        <div class="status-badge">设备运转</div>
                        <div class="status-text">正常</div>
                    </div>

                    <button class="power-button" onclick="togglePower()">
                        <div class="power-icon">⏻</div>
                    </button>

                    <div class="speed-control">
                        <div class="speed-label">档位</div>
                        <div class="speed-value" id="speedValue">低</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = true;
        let currentSpeed = 0; // 0: 低, 1: 中, 2: 高
        const speedLevels = ['低', '中', '高'];

        function togglePower() {
            isRunning = !isRunning;
            const statusBadge = document.querySelector('.status-badge');
            const powerButton = document.querySelector('.power-button');

            if (isRunning) {
                statusBadge.textContent = '设备运转';
                statusBadge.style.background = '#10b981';
                powerButton.style.background = '#f97316';
            } else {
                statusBadge.textContent = '已停止';
                statusBadge.style.background = '#6b7280';
                powerButton.style.background = '#6b7280';
            }
        }

        function refreshWeather() {
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.textContent = '刷新中...';
            refreshBtn.disabled = true;

            // 模拟刷新延迟
            setTimeout(() => {
                refreshBtn.textContent = '刷新';
                refreshBtn.disabled = false;

                // 这里可以添加实际的天气数据更新逻辑
                console.log('天气数据已刷新');
            }, 1000);
        }

        // 点击档位切换
        document.addEventListener('DOMContentLoaded', function() {
            const speedControl = document.querySelector('.speed-control');
            speedControl.addEventListener('click', function() {
                if (isRunning) {
                    currentSpeed = (currentSpeed + 1) % 3;
                    document.getElementById('speedValue').textContent = speedLevels[currentSpeed];
                }
            });
        });
    </script>
</body>
</html>
