<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 390px;
            height: 844px;
            background: #f8f9fa;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #000;
            font-weight: 600;
            z-index: 100;
        }

        .time {
            font-size: 17px;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            position: relative;
            top: 44px;
            padding: 20px;
            background: #f8f9fa;
            min-height: calc(100vh - 44px);
        }

        /* 顶部信息 */
        .top-info {
            text-align: center;
            margin-bottom: 20px;
            position: relative;
        }

        .protection-text {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 4px;
        }

        .protection-status {
            font-size: 12px;
            color: #9ca3af;
            font-weight: 400;
        }

        .refresh-btn {
            position: absolute;
            top: 0;
            right: 0;
            background: none;
            border: none;
            color: #9ca3af;
            font-size: 12px;
            cursor: pointer;
            padding: 4px 8px;
        }

        /* 1. 上半部分 */
        .top-section {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        /* 半圆弧容器 */
        .arc-container {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 320px;
            height: 160px;
        }

        /* 空气质量显示 */
        .air-quality-display {
            position: absolute;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            z-index: 10;
        }

        .air-quality-label {
            font-size: 16px;
            color: #10b981;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .air-quality-value {
            font-size: 64px;
            color: #1f2937;
            font-weight: 900;
            line-height: 0.9;
            margin-bottom: 8px;
        }

        .air-quality-unit {
            font-size: 12px;
            color: #6b7280;
            font-weight: 400;
        }

        /* 汽车图片 */
        .car-image {
            position: absolute;
            width: 280px;
            height: 100px;
            top: 200px;
            left: 50%;
            transform: translateX(-50%);
            object-fit: contain;
        }

        /* 2. 数据指标行 */
        .metrics-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 0 30px;
        }

        .metric-item {
            text-align: center;
            color: #333;
            flex: 1;
        }

        .metric-value {
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 6px;
            line-height: 1;
        }

        .metric-label {
            font-size: 11px;
            color: #666;
            line-height: 1.2;
        }

        /* 3. 天气卡片 */
        .weather-card {
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .weather-main {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        .weather-left {
            flex: 1;
        }

        .city-name {
            font-size: 28px;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 8px;
        }

        .temperature {
            font-size: 52px;
            font-weight: 900;
            color: #10b981;
            line-height: 0.9;
        }

        .weather-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .weather-info {
            text-align: right;
        }

        .weather-detail {
            font-size: 11px;
            color: #666;
            margin-bottom: 2px;
            line-height: 1.3;
        }

        .weather-data {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .weather-data-item {
            text-align: center;
            flex: 1;
        }

        .data-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .data-value {
            font-size: 32px;
            font-weight: 900;
            line-height: 1;
        }

        .data-value.orange {
            color: #f97316;
        }

        .data-value.green {
            color: #10b981;
        }

        .data-value.dark-green {
            color: #15803d;
        }

        /* 4. 设备控制面板 */
        .device-control-panel {
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .control-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .device-status {
            text-align: center;
            flex: 1;
        }

        .status-badge {
            background: #10b981;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 6px;
            display: inline-block;
        }

        .status-text {
            font-size: 11px;
            color: #666;
        }

        .power-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #f97316;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 15px;
            border: none;
        }

        .power-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(249, 115, 22, 0.3);
        }

        .power-icon {
            font-size: 28px;
            color: white;
        }

        .speed-control {
            text-align: center;
            flex: 1;
        }

        .speed-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .speed-value {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        /* 空气粒子效果 */
        .air-particles {
            position: absolute;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 120px;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #10b981;
            border-radius: 50%;
            opacity: 0.6;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
            50% { transform: translateY(-10px) rotate(180deg); opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time">9:41</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 顶部信息 -->
            <div class="top-info">
                <div class="protection-text">已为您健康守护 231 天</div>
                <div class="protection-status">设置 清洗提醒</div>
                <button class="refresh-btn" onclick="refreshWeather()">刷新</button>
            </div>

            <!-- 1. 上半部分：彩色圆弧 + 空气质量 + 汽车 -->
            <div class="top-section">
                <!-- 半圆弧渐变 -->
                <div class="arc-container">
                    <svg width="320" height="160" viewBox="0 0 320 160">
                        <defs>
                            <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
                                <stop offset="25%" style="stop-color:#fbbf24;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
                                <stop offset="75%" style="stop-color:#ef4444;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <path d="M 40 140 A 120 120 0 0 1 280 140"
                              stroke="url(#arcGradient)"
                              stroke-width="20"
                              fill="none"
                              stroke-linecap="round"/>
                        
                        <!-- 指示器圆点 -->
                        <circle cx="140" cy="140" r="8" fill="#374151"/>
                        <circle cx="140" cy="140" r="3" fill="white"/>
                    </svg>
                </div>

                <!-- 空气质量中心指示器 -->
                <div class="air-quality-display">
                    <div class="air-quality-label">空气优</div>
                    <div class="air-quality-value">25</div>
                    <div class="air-quality-unit">车内综合空气质量</div>
                </div>

                <!-- 空气粒子效果 -->
                <div class="air-particles">
                    <div class="particle" style="left: 20%; animation-delay: 0s;"></div>
                    <div class="particle" style="left: 40%; animation-delay: 0.5s;"></div>
                    <div class="particle" style="left: 60%; animation-delay: 1s;"></div>
                    <div class="particle" style="left: 80%; animation-delay: 1.5s;"></div>
                    <div class="particle" style="left: 30%; top: 30%; animation-delay: 0.3s;"></div>
                    <div class="particle" style="left: 70%; top: 40%; animation-delay: 0.8s;"></div>
                </div>

                <!-- 汽车图片 -->
                <div class="car-image">
                    <svg width="280" height="100" viewBox="0 0 280 100">
                        <defs>
                            <linearGradient id="carGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#e0f2fe;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#b3e5fc;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <!-- 汽车主体 -->
                        <path d="M60 60 Q60 40 80 40 L200 40 Q220 40 220 60 L220 70 Q220 80 210 80 L70 80 Q60 80 60 70 Z"
                              fill="url(#carGradient)"
                              stroke="rgba(255,255,255,0.5)"
                              stroke-width="1"/>
                        <!-- 车窗 -->
                        <rect x="90" y="50" width="100" height="12" rx="3" fill="rgba(255,255,255,0.3)"/>
                        <!-- 前轮 -->
                        <circle cx="100" cy="80" r="10" fill="#374151"/>
                        <circle cx="100" cy="80" r="6" fill="#6b7280"/>
                        <!-- 后轮 -->
                        <circle cx="180" cy="80" r="10" fill="#374151"/>
                        <circle cx="180" cy="80" r="6" fill="#6b7280"/>
                        <!-- 车灯 -->
                        <ellipse cx="230" cy="60" rx="5" ry="3" fill="rgba(255,255,255,0.8)"/>
                        <ellipse cx="50" cy="60" rx="5" ry="3" fill="rgba(255,255,255,0.8)"/>
                    </svg>
                </div>
            </div>

            <!-- 2. 数据指标行 -->
            <div class="metrics-row">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 3. 天气卡片 -->
            <div class="weather-card">
                <div class="weather-main">
                    <div class="weather-left">
                        <div class="city-name">深圳市</div>
                        <div class="temperature">33°C</div>
                    </div>
                    <div class="weather-right">
                        <div class="weather-info">
                            <div class="weather-detail">空气质量：中</div>
                            <div class="weather-detail">湿度：52%</div>
                            <div class="weather-detail">能见度：30km</div>
                        </div>
                    </div>
                </div>
                
                <div class="weather-data">
                    <div class="weather-data-item">
                        <div class="data-label">车外PM2.5</div>
                        <div class="data-value orange">105</div>
                    </div>
                    <div class="weather-data-item">
                        <div class="data-label">车内PM2.5</div>
                        <div class="data-value green">14</div>
                    </div>
                    <div class="weather-data-item">
                        <div class="data-label">车外负氧离子</div>
                        <div class="data-value dark-green">628</div>
                    </div>
                </div>
            </div>

            <!-- 4. 设备控制面板 -->
            <div class="device-control-panel">
                <div class="control-container">
                    <div class="device-status">
                        <div class="status-badge">设备运转</div>
                        <div class="status-text">正常</div>
                    </div>

                    <button class="power-button" onclick="togglePower()">
                        <div class="power-icon">⏻</div>
                    </button>

                    <div class="speed-control">
                        <div class="speed-label">档位</div>
                        <div class="speed-value" id="speedValue">高</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = true;
        let currentSpeed = 2; // 0: 低, 1: 中, 2: 高
        const speedLevels = ['低', '中', '高'];

        function togglePower() {
            isRunning = !isRunning;
            const statusBadge = document.querySelector('.status-badge');
            const powerButton = document.querySelector('.power-button');

            if (isRunning) {
                statusBadge.textContent = '设备运转';
                statusBadge.style.background = '#10b981';
                powerButton.style.background = '#f97316';
            } else {
                statusBadge.textContent = '已停止';
                statusBadge.style.background = '#6b7280';
                powerButton.style.background = '#6b7280';
            }
        }

        function refreshWeather() {
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.textContent = '刷新中...';
            refreshBtn.disabled = true;

            // 模拟刷新延迟
            setTimeout(() => {
                refreshBtn.textContent = '刷新';
                refreshBtn.disabled = false;

                // 这里可以添加实际的天气数据更新逻辑
                console.log('天气数据已刷新');
            }, 1000);
        }

        // 点击档位切换
        document.addEventListener('DOMContentLoaded', function() {
            const speedControl = document.querySelector('.speed-control');
            speedControl.addEventListener('click', function() {
                if (isRunning) {
                    currentSpeed = (currentSpeed + 1) % 3;
                    document.getElementById('speedValue').textContent = speedLevels[currentSpeed];
                }
            });
        });
    </script>
</body>
</html>
