<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 390px;
            height: 844px;
            background: #ffffff;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left {
            font-size: 17px;
            font-weight: 600;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 60px 20px 20px;
            height: 100%;
        }

        /* 顶部区域 */
        .top-section {
            text-align: center;
            margin-bottom: 40px;
        }

        /* 守护天数 */
        .protection-info {
            margin-bottom: 30px;
        }

        .protection-days {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .protection-status {
            font-size: 14px;
            color: #ff6b35;
        }

        /* 半圆弧容器 */
        .arc-container {
            position: relative;
            width: 280px;
            height: 140px;
            margin: 0 auto 30px;
        }

        /* 半圆弧 */
        .arc-background {
            width: 280px;
            height: 140px;
            border: 20px solid #f0f0f0;
            border-bottom: none;
            border-radius: 140px 140px 0 0;
        }

        .arc-progress {
            position: absolute;
            top: 0;
            left: 0;
            width: 280px;
            height: 140px;
            border: 20px solid;
            border-bottom: none;
            border-radius: 140px 140px 0 0;
            border-image: linear-gradient(90deg,
                #4ECDC4 0%,
                #FFD700 25%,
                #FFA500 50%,
                #FF6B35 75%,
                #E74C3C 100%) 1;
        }

        /* 空气质量显示 */
        .air-quality-display {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .air-quality-label {
            font-size: 16px;
            color: #666;
            margin-bottom: 5px;
        }

        .air-quality-value {
            font-size: 80px;
            color: #333;
            font-weight: 900;
            line-height: 1;
            margin-bottom: 5px;
        }

        .air-quality-unit {
            font-size: 14px;
            color: #888;
        }

        /* 汽车图标 */
        .car-container {
            margin-bottom: 40px;
            text-align: center;
        }

        .car-icon {
            width: 240px;
            height: 60px;
            background: #4ECDC4;
            border-radius: 30px;
            position: relative;
            margin: 0 auto;
            box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
        }

        /* 车轮 */
        .car-icon::before,
        .car-icon::after {
            content: '';
            position: absolute;
            width: 40px;
            height: 40px;
            background: #555;
            border-radius: 50%;
            bottom: -8px;
            box-shadow: inset 0 0 0 8px #333;
        }

        .car-icon::before {
            left: 30px;
        }

        .car-icon::after {
            right: 30px;
        }

        /* 车窗装饰 */
        .car-icon::after {
            box-shadow: inset 0 0 0 8px #333, 0 0 0 2px #4ECDC4;
        }

        /* 数据指标行 */
        .metrics-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .metric-item {
            text-align: center;
        }

        .metric-value {
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 5px;
            line-height: 1;
        }

        .metric-value.pm25 { color: #333; }
        .metric-value.formaldehyde { color: #ff6b35; }
        .metric-value.negative-ions { color: #333; }

        .metric-label {
            font-size: 12px;
            color: #888;
        }

        /* 天气卡片 */
        .weather-card {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .weather-location {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .weather-temp {
            font-size: 48px;
            font-weight: 900;
            color: #333;
            line-height: 1;
        }

        .weather-info {
            text-align: right;
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .weather-metrics {
            display: flex;
            justify-content: space-around;
        }

        .weather-metric {
            text-align: center;
        }

        .weather-metric-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .weather-metric-value {
            font-size: 28px;
            font-weight: 700;
        }

        .weather-metric-value.outdoor-pm25 { color: #ff6b35; }
        .weather-metric-value.indoor-pm25 { color: #4ECDC4; }
        .weather-metric-value.indoor-ions { color: #2ECC71; }

        /* 控制面板 */
        .control-panel {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .device-button {
            background: #4ECDC4;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        .device-status {
            font-size: 14px;
            color: #666;
        }

        .power-button {
            width: 60px;
            height: 60px;
            background: #ff6b35;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-right {
            text-align: center;
        }

        .档位-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .档位-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">9:41</div>
            <div class="status-right">
                <span>●●●</span>
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 顶部区域 -->
            <div class="top-section">
                <!-- 守护天数信息 -->
                <div class="protection-info">
                    <div class="protection-days">已为您健康守护 231 天</div>
                    <div class="protection-status">设备 清洁健康</div>
                </div>

                <!-- 半圆弧和空气质量 -->
                <div class="arc-container">
                    <div class="arc-background"></div>
                    <div class="arc-progress"></div>
                    <div class="air-quality-display">
                        <div class="air-quality-label">空气优</div>
                        <div class="air-quality-value">25</div>
                        <div class="air-quality-unit">车内综合空气质量</div>
                    </div>
                </div>

                <!-- 汽车图标 -->
                <div class="car-container">
                    <div class="car-icon"></div>
                </div>
            </div>

            <!-- 数据指标行 -->
            <div class="metrics-row">
                <div class="metric-item">
                    <div class="metric-value pm25">014</div>
                    <div class="metric-label">PM2.5(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value formaldehyde">36</div>
                    <div class="metric-label">甲醛(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value negative-ions">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 天气卡片 -->
            <div class="weather-card">
                <div class="weather-header">
                    <div>
                        <div class="weather-location">深圳市</div>
                        <div class="weather-temp">33°C</div>
                    </div>
                    <div class="weather-info">
                        空气质量：优<br>
                        湿度：52%<br>
                        能见度：30km
                    </div>
                </div>
                <div class="weather-metrics">
                    <div class="weather-metric">
                        <div class="weather-metric-label">车外PM2.5</div>
                        <div class="weather-metric-value outdoor-pm25">105</div>
                    </div>
                    <div class="weather-metric">
                        <div class="weather-metric-label">车内PM2.5</div>
                        <div class="weather-metric-value indoor-pm25">14</div>
                    </div>
                    <div class="weather-metric">
                        <div class="weather-metric-label">车内负氧离子</div>
                        <div class="weather-metric-value indoor-ions">628</div>
                    </div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-left">
                    <button class="device-button">设备运转</button>
                    <div class="device-status">正常</div>
                </div>
                <button class="power-button">⏻</button>
                <div class="control-right">
                    <div class="档位-label">档位</div>
                    <div class="档位-value">低</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
