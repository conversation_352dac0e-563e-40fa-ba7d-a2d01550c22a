<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iPhone空气质量监测 - 完整HTML版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 393px;
            height: 852px;
            background: #f5f5f5;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            width: 375px;
            height: 44px;
            top: 0;
            left: 11px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #000;
            font-weight: 600;
            z-index: 100;
        }

        .time {
            font-size: 17px;
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            position: absolute;
            width: 393px;
            height: 783px;
            top: 69px;
            left: 0;
            background: #f5f5f5;
        }

        /* 背景图片 */
        .background-image {
            position: absolute;
            width: 393px;
            height: 783px;
            top: 0;
            left: 0;
            object-fit: cover;
        }

        /* 圆形渐变装饰元素 */
        .circular-container {
            position: absolute;
            width: 393px;
            height: 300px;
            top: 0;
            left: 0;
        }

        .outer-ellipse, .middle-ellipse, .inner-ellipse {
            position: absolute;
            border-radius: 50%;
        }

        .outer-ellipse {
            width: 300px;
            height: 300px;
            top: -50px;
            left: 46px;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 140, 0, 0.3) 25%, rgba(0, 229, 204, 0.3) 50%, rgba(255, 68, 68, 0.3) 100%);
        }

        .middle-ellipse {
            width: 250px;
            height: 250px;
            top: -25px;
            left: 71px;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 140, 0, 0.2) 25%, rgba(0, 229, 204, 0.2) 50%, rgba(255, 68, 68, 0.2) 100%);
        }

        .inner-ellipse {
            width: 200px;
            height: 200px;
            top: 0;
            left: 96px;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 140, 0, 0.1) 25%, rgba(0, 229, 204, 0.1) 50%, rgba(255, 68, 68, 0.1) 100%);
        }

        /* 汽车图片 */
        .car-image {
            position: absolute;
            width: 315px;
            height: 210px;
            top: 69px;
            left: 39px;
            object-fit: cover;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 315 210"><defs><linearGradient id="carGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%2300d4aa"/><stop offset="100%" style="stop-color:%2300a693"/></linearGradient></defs><g transform="translate(20,80)"><path d="M40 70 Q40 60 50 60 L80 60 Q90 50 100 50 L180 50 Q190 50 200 60 L230 60 Q240 60 240 70 L240 85 Q240 95 230 95 L50 95 Q40 95 40 85 Z" fill="url(%23carGrad)"/><circle cx="80" cy="85" r="14" fill="%23374151"/><circle cx="200" cy="85" r="14" fill="%23374151"/><rect x="100" y="55" width="80" height="25" rx="3" fill="rgba(255,255,255,0.4)"/></g></svg>') center/contain no-repeat;
        }

        /* 空气质量中心 */
        .air-quality-center {
            position: absolute;
            width: 75px;
            height: 96px;
            top: 49px;
            left: 166px;
            z-index: 10;
        }

        .indicator-content {
            position: absolute;
            width: 69px;
            height: 87px;
            top: 9px;
            left: 0;
        }

        .air-status {
            position: absolute;
            top: 0;
            left: 4px;
            color: #454545;
            font-size: 20px;
            white-space: nowrap;
            font-weight: normal;
        }

        .air-level {
            position: absolute;
            top: 30px;
            left: 0;
            color: #1b705f;
            font-size: 48px;
            font-weight: bold;
        }

        .air-description {
            position: absolute;
            top: 0;
            left: 10px;
            color: #454545;
            font-size: 6px;
            white-space: nowrap;
            font-weight: normal;
        }

        /* 健康守护信息 */
        .health-protection {
            position: absolute;
            top: 279px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #8b5cf6;
            font-size: 14px;
        }

        .days-number {
            font-weight: bold;
        }

        /* 清洗提醒设置 */
        .cleaning-reminder {
            position: absolute;
            top: 305px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }

        .reminder-text {
            color: #666;
            font-size: 12px;
        }

        .reminder-status {
            color: #10b981;
            font-size: 12px;
            font-weight: 500;
        }

        /* 空气质量指标网格 */
        .metrics-container {
            position: absolute;
            top: 340px;
            left: 0;
            width: 393px;
            display: flex;
            justify-content: space-around;
            padding: 0 30px;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-content {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 6px;
            line-height: 1;
        }

        .metric-label {
            font-size: 11px;
            color: #6b7280;
            line-height: 1.2;
        }

        /* 天气信息卡片 */
        .weather-card {
            position: absolute;
            top: 430px;
            left: 20px;
            right: 20px;
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .refresh-button {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: #6b7280;
            font-size: 12px;
            cursor: pointer;
            padding: 5px;
        }

        .top-box {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .city-temp-section {
            flex: 1;
        }

        .city-temp-line {
            display: flex;
            align-items: baseline;
            gap: 8px;
            margin-bottom: 4px;
        }

        .city-name {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
        }

        .temp-number {
            font-size: 48px;
            font-weight: 900;
            color: #1f2937;
            line-height: 1;
        }

        .temp-symbol {
            font-size: 24px;
            color: #1f2937;
        }

        .publish-info {
            font-size: 10px;
            color: #6b7280;
        }

        .env-section {
            text-align: right;
        }

        .env-line {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }

        .bottom-box {
            display: flex;
            justify-content: space-around;
        }

        .data-section {
            text-align: center;
            flex: 1;
        }

        .data-label {
            font-size: 11px;
            color: #6b7280;
            margin-bottom: 6px;
        }

        .data-value {
            font-size: 28px;
            font-weight: 900;
            line-height: 1;
        }

        .data-value.orange { color: #f97316; }
        .data-value.green { color: #10b981; }
        .data-value.dark-green { color: #059669; }

        /* 设备控制面板 */
        .device-control-panel {
            position: absolute;
            bottom: 30px;
            left: 20px;
            right: 20px;
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .control-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .device-status {
            text-align: center;
            flex: 1;
        }

        .status-badge {
            background: #10b981;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 6px;
            display: inline-block;
        }

        .status-text {
            font-size: 12px;
            color: #6b7280;
        }

        .power-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .speed-control {
            text-align: center;
            flex: 1;
        }

        .speed-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 6px;
        }

        .speed-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .speed-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            font-size: 16px;
            cursor: pointer;
        }

        .speed-value {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            min-width: 30px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time">9:41</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 背景图片 -->
            <div class="background-image"></div>

            <!-- 圆形渐变装饰元素 -->
            <div class="circular-container">
                <div class="outer-ellipse"></div>
                <div class="middle-ellipse"></div>
                <div class="inner-ellipse"></div>
            </div>

            <!-- 汽车可视化 -->
            <div class="car-image"></div>

            <!-- 空气质量中心指示器 -->
            <div class="air-quality-center">
                <div class="indicator-content">
                    <div class="air-status">空气优</div>
                    <div class="air-level">25</div>
                </div>
                <div class="air-description">车内综合空气质量</div>
            </div>

            <!-- 健康守护信息 -->
            <div class="health-protection">
                <span class="protection-text">已为您健康守护 </span>
                <span class="days-number">231</span>
                <span class="protection-text"> 天</span>
            </div>

            <!-- 清洗提醒设置 -->
            <div class="cleaning-reminder">
                <div class="reminder-text">设备</div>
                <div class="reminder-status">清洁健康</div>
            </div>

            <!-- 空气质量指标网格 -->
            <div class="metrics-container">
                <div class="metric-item">
                    <div class="metric-content">
                        <div class="metric-value">014</div>
                        <div class="metric-label">PM2.5(µg/m³）</div>
                    </div>
                </div>
                <div class="metric-item">
                    <div class="metric-content">
                        <div class="metric-value">36</div>
                        <div class="metric-label">甲醛(µg/m³）</div>
                    </div>
                </div>
                <div class="metric-item">
                    <div class="metric-content">
                        <div class="metric-value">25500</div>
                        <div class="metric-label">负氧离子(个/cm³）</div>
                    </div>
                </div>
            </div>

            <!-- 天气信息卡片 -->
            <div class="weather-card">
                <button class="refresh-button" onclick="refreshWeather()">刷新</button>
                
                <!-- 上盒子：城市温度和环境信息 -->
                <div class="top-box">
                    <div class="city-temp-section">
                        <div class="city-temp-line">
                            <span class="city-name">深圳市</span>
                            <span class="temp-number">33</span>
                            <span class="temp-symbol">°C</span>
                        </div>
                        <div class="publish-info">2025-06-09 15:07:03发布</div>
                    </div>

                    <div class="env-section">
                        <div class="env-line">空气质量: 优</div>
                        <div class="env-line">湿度: 52%</div>
                        <div class="env-line">能见度: 30km</div>
                    </div>
                </div>

                <!-- 下盒子：PM2.5数据指标 -->
                <div class="bottom-box">
                    <div class="data-section">
                        <div class="data-label">车外PM2.5</div>
                        <div class="data-value orange">105</div>
                    </div>

                    <div class="data-section">
                        <div class="data-label">车内PM2.5</div>
                        <div class="data-value green">14</div>
                    </div>

                    <div class="data-section">
                        <div class="data-label">车内负氧离子</div>
                        <div class="data-value dark-green">628</div>
                    </div>
                </div>
            </div>

            <!-- 设备控制面板 -->
            <div class="device-control-panel">
                <div class="control-container">
                    <div class="device-status">
                        <div class="status-badge">设备运转</div>
                        <div class="status-text">正常</div>
                    </div>
                    
                    <button class="power-button" onclick="togglePower()">⏻</button>
                    
                    <div class="speed-control">
                        <div class="speed-label">档位</div>
                        <div class="speed-buttons">
                            <button class="speed-btn" onclick="decreaseSpeed()">-</button>
                            <span class="speed-value" id="speedValue">低</span>
                            <button class="speed-btn" onclick="increaseSpeed()">+</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互功能
        let currentSpeed = 0;
        const speedLevels = ['低', '中', '高'];
        let isRunning = true;

        function refreshWeather() {
            const refreshBtn = document.querySelector('.refresh-button');
            refreshBtn.textContent = '刷新中...';
            refreshBtn.disabled = true;

            setTimeout(() => {
                refreshBtn.textContent = '刷新';
                refreshBtn.disabled = false;
                console.log('天气数据已刷新');
            }, 1000);
        }

        function togglePower() {
            isRunning = !isRunning;
            const statusBadge = document.querySelector('.status-badge');
            const statusText = document.querySelector('.status-text');
            
            if (isRunning) {
                statusBadge.textContent = '设备运转';
                statusBadge.style.background = '#10b981';
                statusText.textContent = '正常';
            } else {
                statusBadge.textContent = '设备停止';
                statusBadge.style.background = '#ef4444';
                statusText.textContent = '已停止';
            }
        }

        function increaseSpeed() {
            if (isRunning && currentSpeed < 2) {
                currentSpeed++;
                document.getElementById('speedValue').textContent = speedLevels[currentSpeed];
            }
        }

        function decreaseSpeed() {
            if (isRunning && currentSpeed > 0) {
                currentSpeed--;
                document.getElementById('speedValue').textContent = speedLevels[currentSpeed];
            }
        }
    </script>
</body>
</html>
