<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空气质量监测 - 精美版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 390px;
            height: 844px;
            background: #ffffff;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #000;
            font-weight: 600;
            z-index: 100;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
        }

        .time {
            font-size: 17px;
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            position: relative;
            top: 44px;
            padding: 20px;
            background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
            min-height: calc(100vh - 44px);
        }

        /* 顶部信息 */
        .top-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .protection-text {
            font-size: 14px;
            color: #8b5cf6;
            margin-bottom: 4px;
        }

        .device-status {
            font-size: 12px;
            color: #10b981;
            font-weight: 500;
        }

        .refresh-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            padding: 8px;
        }

        /* 半圆弧容器 */
        .arc-container {
            position: relative;
            width: 320px;
            height: 160px;
            margin: 0 auto 20px;
        }

        /* 背景光晕 */
        .glow-background {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 50%, transparent 100%);
            border-radius: 50%;
        }

        /* 空气质量中心显示 */
        .air-quality-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .air-status {
            font-size: 16px;
            color: #10b981;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .air-value {
            font-size: 72px;
            color: #1f2937;
            font-weight: 900;
            line-height: 0.9;
            margin-bottom: 8px;
        }

        .air-description {
            font-size: 12px;
            color: #6b7280;
            font-weight: 400;
        }

        /* 3D汽车图片 */
        .car-container {
            position: relative;
            width: 280px;
            height: 120px;
            margin: 20px auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .car-svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 8px 25px rgba(0, 0, 0, 0.15));
        }

        /* 数据指标行 */
        .metrics-row {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            padding: 0 20px;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 6px;
            line-height: 1;
        }

        .metric-label {
            font-size: 11px;
            color: #6b7280;
            line-height: 1.2;
        }

        /* 天气卡片 */
        .weather-card {
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .weather-main {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .city-name {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .temperature {
            font-size: 48px;
            font-weight: 900;
            color: #1f2937;
            line-height: 1;
        }

        .weather-info {
            text-align: right;
        }

        .weather-detail {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }

        /* 对比数据 */
        .comparison-data {
            display: flex;
            justify-content: space-around;
        }

        .data-item {
            text-align: center;
            flex: 1;
        }

        .data-label {
            font-size: 11px;
            color: #6b7280;
            margin-bottom: 6px;
        }

        .data-value {
            font-size: 28px;
            font-weight: 900;
            line-height: 1;
        }

        .data-value.orange { color: #f97316; }
        .data-value.green { color: #10b981; }
        .data-value.dark-green { color: #059669; }

        /* 控制面板 */
        .control-panel {
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .device-control {
            text-align: center;
            flex: 1;
        }

        .control-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }

        .control-status {
            font-size: 14px;
            font-weight: 600;
            color: #10b981;
        }

        .power-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
        }

        .level-control {
            text-align: center;
            flex: 1;
        }

        .level-value {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time">9:41</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 顶部信息 -->
            <div class="top-info">
                <div class="protection-text">已为您健康守护 231 天</div>
                <div class="device-status">设备 清洁健康</div>
            </div>
            
            <button class="refresh-btn">刷新</button>

            <!-- 半圆弧和空气质量显示 -->
            <div class="arc-container">
                <!-- 背景光晕 -->
                <div class="glow-background"></div>
                
                <!-- SVG半圆弧 -->
                <svg width="320" height="160" viewBox="0 0 320 160" style="position: absolute; top: 0; left: 0;">
                    <defs>
                        <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
                            <stop offset="25%" style="stop-color:#f97316;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#10b981;stop-opacity:1" />
                            <stop offset="75%" style="stop-color:#06b6d4;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <path d="M 40 140 A 120 120 0 0 1 280 140"
                          stroke="url(#arcGradient)"
                          stroke-width="16"
                          fill="none"
                          stroke-linecap="round"/>
                </svg>

                <!-- 空气质量中心显示 -->
                <div class="air-quality-center">
                    <div class="air-status">空气优</div>
                    <div class="air-value">25</div>
                    <div class="air-description">车内综合空气质量</div>
                </div>
            </div>

            <!-- 3D汽车 -->
            <div class="car-container">
                <svg class="car-svg" viewBox="0 0 280 120" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <!-- 汽车主体渐变 -->
                        <linearGradient id="carBodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#00d4aa;stop-opacity:1" />
                            <stop offset="30%" style="stop-color:#00c4a7;stop-opacity:1" />
                            <stop offset="70%" style="stop-color:#00b4a6;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#00a693;stop-opacity:1" />
                        </linearGradient>

                        <!-- 车窗渐变 -->
                        <linearGradient id="windowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:rgba(255,255,255,0.6);stop-opacity:1" />
                            <stop offset="50%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
                            <stop offset="100%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
                        </linearGradient>

                        <!-- 轮胎渐变 -->
                        <radialGradient id="wheelGradient" cx="50%" cy="30%" r="70%">
                            <stop offset="0%" style="stop-color:#4a5568;stop-opacity:1" />
                            <stop offset="70%" style="stop-color:#2d3748;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#1a202c;stop-opacity:1" />
                        </radialGradient>

                        <!-- 阴影滤镜 -->
                        <filter id="carShadow" x="-50%" y="-50%" width="200%" height="200%">
                            <feDropShadow dx="0" dy="6" stdDeviation="8" flood-color="#000000" flood-opacity="0.2"/>
                        </filter>
                    </defs>

                    <!-- 汽车阴影 -->
                    <ellipse cx="140" cy="105" rx="120" ry="8" fill="rgba(0,0,0,0.1)"/>

                    <!-- 汽车主体 -->
                    <g filter="url(#carShadow)">
                        <!-- 车身主体 -->
                        <path d="M45 75 Q45 65 55 65 L85 65 Q95 55 110 55 L170 55 Q185 55 195 65 L225 65 Q235 65 235 75 L235 85 Q235 90 230 90 L50 90 Q45 90 45 85 Z"
                              fill="url(#carBodyGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="0.5"/>

                        <!-- 前挡风玻璃 -->
                        <path d="M95 65 Q105 58 115 58 L165 58 Q175 58 185 65 L175 70 Q165 63 115 63 Q105 63 95 70 Z"
                              fill="url(#windowGradient)"/>

                        <!-- 侧窗 -->
                        <rect x="120" y="63" width="40" height="15" rx="2" fill="url(#windowGradient)"/>

                        <!-- 车门线条 -->
                        <line x1="110" y1="70" x2="110" y2="85" stroke="rgba(255,255,255,0.4)" stroke-width="1"/>
                        <line x1="170" y1="70" x2="170" y2="85" stroke="rgba(255,255,255,0.4)" stroke-width="1"/>

                        <!-- 前轮 -->
                        <circle cx="85" cy="85" r="14" fill="url(#wheelGradient)"/>
                        <circle cx="85" cy="85" r="8" fill="#1a202c"/>
                        <circle cx="85" cy="85" r="4" fill="#4a5568"/>

                        <!-- 后轮 -->
                        <circle cx="195" cy="85" r="14" fill="url(#wheelGradient)"/>
                        <circle cx="195" cy="85" r="8" fill="#1a202c"/>
                        <circle cx="195" cy="85" r="4" fill="#4a5568"/>

                        <!-- 前灯 -->
                        <ellipse cx="45" cy="75" rx="4" ry="6" fill="rgba(255,255,255,0.9)"/>

                        <!-- 尾灯 -->
                        <ellipse cx="235" cy="75" rx="3" ry="5" fill="rgba(255,100,100,0.8)"/>

                        <!-- 车顶高光 -->
                        <path d="M55 65 Q140 60 225 65" stroke="rgba(255,255,255,0.6)" stroke-width="2" fill="none"/>
                    </g>
                </svg>
            </div>

            <!-- 数据指标行 -->
            <div class="metrics-row">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 天气卡片 -->
            <div class="weather-card">
                <div class="weather-main">
                    <div class="weather-left">
                        <div class="city-name">深圳市</div>
                        <div class="temperature">33°C</div>
                    </div>
                    <div class="weather-info">
                        <div class="weather-detail">空气质量：优</div>
                        <div class="weather-detail">湿度：52%</div>
                        <div class="weather-detail">能见度：30km</div>
                    </div>
                </div>
                
                <div class="comparison-data">
                    <div class="data-item">
                        <div class="data-label">车外PM2.5</div>
                        <div class="data-value orange">105</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">车内PM2.5</div>
                        <div class="data-value green">14</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">车内负氧离子</div>
                        <div class="data-value dark-green">628</div>
                    </div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="device-control">
                    <div class="control-label">设备运转</div>
                    <div class="control-status">正常</div>
                </div>
                
                <button class="power-button">⏻</button>
                
                <div class="level-control">
                    <div class="control-label">档位</div>
                    <div class="level-value">低</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
