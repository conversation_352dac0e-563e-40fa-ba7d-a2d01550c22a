<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 390px;
            height: 844px;
            background: #ffffff;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 60px 20px 20px;
            height: 100%;
        }

        /* 顶部区域 - 守护天数和刷新按钮 */
        .top-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
        }

        .protection-info {
            flex: 1;
        }

        .protection-days {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .protection-status {
            font-size: 14px;
            color: #ff6b35;
        }

        .refresh-btn {
            background: none;
            border: none;
            font-size: 16px;
            color: #666;
            cursor: pointer;
        }

        /* 半圆弧和空气质量区域 */
        .arc-quality-section {
            position: relative;
            height: 280px;
            margin-bottom: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 半圆弧 - 作为背景 */
        .arc-background {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 空气质量显示 - 在半圆弧中心 */
        .air-quality-display {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            z-index: 10;
        }

        .air-quality-label {
            font-size: 16px;
            color: #666;
            margin-bottom: 8px;
        }

        .air-quality-value {
            font-size: 120px;
            color: #333;
            font-weight: 900;
            line-height: 0.8;
            margin-bottom: 8px;
        }

        .air-quality-unit {
            font-size: 14px;
            color: #888;
        }

        /* 汽车图标 - 在半圆弧下方 */
        .car-display {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 数据指标行 */
        .metrics-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 40px;
            padding: 0 20px;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 8px;
            line-height: 1;
        }

        .metric-value.pm25 { color: #333; }
        .metric-value.formaldehyde { color: #ff6b35; }
        .metric-value.negative-ions { color: #333; }

        .metric-label {
            font-size: 12px;
            color: #888;
        }

        /* 天气卡片 */
        .weather-card {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .weather-left {
            flex: 1;
        }

        .weather-location {
            font-size: 32px;
            font-weight: 900;
            color: #333;
            margin-bottom: 5px;
        }

        .weather-temp {
            font-size: 64px;
            font-weight: 900;
            color: #333;
            line-height: 1;
        }

        .weather-info {
            text-align: right;
            font-size: 12px;
            color: #666;
            line-height: 1.6;
        }

        .weather-metrics {
            display: flex;
            justify-content: space-around;
        }

        .weather-metric {
            text-align: center;
            flex: 1;
        }

        .weather-metric-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .weather-metric-value {
            font-size: 36px;
            font-weight: 900;
        }

        .weather-metric-value.outdoor-pm25 { color: #ff6b35; }
        .weather-metric-value.indoor-pm25 { color: #4ECDC4; }
        .weather-metric-value.indoor-ions { color: #2ECC71; }

        /* 控制面板 */
        .control-panel {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .device-button {
            background: #4ECDC4;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        .device-status {
            font-size: 14px;
            color: #666;
        }

        .power-button {
            width: 60px;
            height: 60px;
            background: #ff6b35;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 28px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-right {
            text-align: center;
        }

        .gear-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .gear-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div>9:41</div>
            <div class="status-icons">
                <span>●●●</span>
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 顶部 - 守护天数和刷新按钮 -->
            <div class="top-header">
                <div class="protection-info">
                    <div class="protection-days">已为您健康守护 231 天</div>
                    <div class="protection-status">设备 清洁健康</div>
                </div>
                <button class="refresh-btn">刷新</button>
            </div>

            <!-- 半圆弧和空气质量区域 -->
            <div class="arc-quality-section">
                <!-- 半圆弧背景 -->
                <div class="arc-background">
                    <svg width="320" height="160" viewBox="0 0 320 160">
                        <defs>
                            <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#FFD700"/>
                                <stop offset="25%" style="stop-color:#FFA500"/>
                                <stop offset="50%" style="stop-color:#4ECDC4"/>
                                <stop offset="75%" style="stop-color:#32CD32"/>
                                <stop offset="100%" style="stop-color:#FF6347"/>
                            </linearGradient>
                        </defs>
                        <path d="M 40 130 A 120 120 0 0 1 280 130" 
                              stroke="url(#arcGradient)" 
                              stroke-width="20" 
                              fill="none" 
                              stroke-linecap="round"/>
                    </svg>
                </div>

                <!-- 空气质量显示 -->
                <div class="air-quality-display">
                    <div class="air-quality-label">空气优</div>
                    <div class="air-quality-value">25</div>
                    <div class="air-quality-unit">车内综合空气质量</div>
                </div>

                <!-- 汽车图标 -->
                <div class="car-display">
                    <svg width="240" height="60" viewBox="0 0 240 60">
                        <!-- 车身 -->
                        <rect x="30" y="15" width="180" height="30" rx="15" fill="#4ECDC4"/>
                        <!-- 左轮 -->
                        <circle cx="70" cy="50" r="15" fill="#333"/>
                        <!-- 右轮 -->
                        <circle cx="170" cy="50" r="15" fill="#333"/>
                    </svg>
                </div>
            </div>

            <!-- 数据指标行 -->
            <div class="metrics-row">
                <div class="metric-item">
                    <div class="metric-value pm25">014</div>
                    <div class="metric-label">PM2.5(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value formaldehyde">36</div>
                    <div class="metric-label">甲醛(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value negative-ions">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 天气卡片 -->
            <div class="weather-card">
                <div class="weather-header">
                    <div class="weather-left">
                        <div class="weather-location">深圳市</div>
                        <div class="weather-temp">33°C</div>
                    </div>
                    <div class="weather-info">
                        空气质量：优<br>
                        湿度：52%<br>
                        能见度：30km
                    </div>
                </div>
                <div class="weather-metrics">
                    <div class="weather-metric">
                        <div class="weather-metric-label">车外PM2.5</div>
                        <div class="weather-metric-value outdoor-pm25">105</div>
                    </div>
                    <div class="weather-metric">
                        <div class="weather-metric-label">车内PM2.5</div>
                        <div class="weather-metric-value indoor-pm25">14</div>
                    </div>
                    <div class="weather-metric">
                        <div class="weather-metric-label">车内负氧离子</div>
                        <div class="weather-metric-value indoor-ions">628</div>
                    </div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-left">
                    <button class="device-button">设备运转</button>
                    <div class="device-status">正常</div>
                </div>
                <button class="power-button">⏻</button>
                <div class="control-right">
                    <div class="gear-label">档位</div>
                    <div class="gear-value">低</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
