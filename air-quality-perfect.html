<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* iPhone 容器 */
        .iphone-container {
            width: 390px;
            height: 844px;
            background: #ffffff;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 60px 20px 20px;
            height: 100%;
        }

        /* 顶部区域 - 守护天数和刷新按钮 */
        .top-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
        }

        .protection-info {
            flex: 1;
        }

        .protection-days {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .protection-status {
            font-size: 14px;
            color: #ff6b35;
        }

        .refresh-btn {
            background: none;
            border: none;
            font-size: 16px;
            color: #666;
            cursor: pointer;
        }

        /* 半圆弧和空气质量区域 */
        .arc-quality-section {
            position: relative;
            height: 280px;
            margin-bottom: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 半圆弧 - 作为背景 */
        .arc-background {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 空气质量显示 - 在半圆弧中心 */
        .air-quality-display {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            z-index: 10;
        }

        .air-quality-label {
            font-size: 16px;
            color: #666;
            margin-bottom: 8px;
        }

        .air-quality-value {
            font-size: 120px;
            color: #333;
            font-weight: 900;
            line-height: 0.8;
            margin-bottom: 8px;
        }

        .air-quality-unit {
            font-size: 14px;
            color: #888;
        }

        /* 汽车图标 - 在半圆弧下方 */
        .car-display {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 数据指标行 */
        .metrics-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 40px;
            padding: 0 20px;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 8px;
            line-height: 1;
        }

        .metric-value.pm25 { color: #333; }
        .metric-value.formaldehyde { color: #ff6b35; }
        .metric-value.negative-ions { color: #333; }

        .metric-label {
            font-size: 12px;
            color: #888;
        }

        /* 天气卡片 */
        .weather-card {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .weather-left {
            flex: 1;
        }

        .weather-location {
            font-size: 32px;
            font-weight: 900;
            color: #333;
            margin-bottom: 5px;
        }

        .weather-temp {
            font-size: 64px;
            font-weight: 900;
            color: #333;
            line-height: 1;
        }

        .weather-info {
            text-align: right;
            font-size: 12px;
            color: #666;
            line-height: 1.6;
        }

        .weather-metrics {
            display: flex;
            justify-content: space-around;
        }

        .weather-metric {
            text-align: center;
            flex: 1;
        }

        .weather-metric-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .weather-metric-value {
            font-size: 36px;
            font-weight: 900;
        }

        .weather-metric-value.outdoor-pm25 { color: #ff6b35; }
        .weather-metric-value.indoor-pm25 { color: #4ECDC4; }
        .weather-metric-value.indoor-ions { color: #2ECC71; }

        /* 控制面板 */
        .control-panel {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .control-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .device-button {
            background: #4ECDC4;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        .device-status {
            font-size: 14px;
            color: #666;
        }

        .power-button {
            width: 60px;
            height: 60px;
            background: #ff6b35;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 28px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-right {
            text-align: center;
        }

        .gear-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .gear-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div>9:41</div>
            <div class="status-icons">
                <span>●●●</span>
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 顶部 - 守护天数和刷新按钮 -->
            <div class="top-header">
                <div class="protection-info">
                    <div class="protection-days">已为您健康守护 231 天</div>
                    <div class="protection-status">设备 清洁健康</div>
                </div>
                <button class="refresh-btn">刷新</button>
            </div>

            <!-- 半圆弧和空气质量区域 -->
            <div class="arc-quality-section">
                <!-- 半圆弧背景 -->
                <div class="arc-background">
                    <svg width="320" height="160" viewBox="0 0 320 160">
                        <defs>
                            <!-- 半圆弧渐变 -->
                            <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#FFD700"/>
                                <stop offset="25%" style="stop-color:#FFA500"/>
                                <stop offset="50%" style="stop-color:#4ECDC4"/>
                                <stop offset="75%" style="stop-color:#32CD32"/>
                                <stop offset="100%" style="stop-color:#FF6347"/>
                            </linearGradient>
                            <!-- 内部填充渐变 -->
                            <radialGradient id="innerGradient" cx="50%" cy="80%" r="60%">
                                <stop offset="0%" style="stop-color:#4ECDC4;stop-opacity:0.3"/>
                                <stop offset="50%" style="stop-color:#4ECDC4;stop-opacity:0.2"/>
                                <stop offset="100%" style="stop-color:#4ECDC4;stop-opacity:0.1"/>
                            </radialGradient>
                        </defs>
                        <!-- 内部填充 -->
                        <path d="M 40 130 A 120 120 0 0 1 280 130 Z"
                              fill="url(#innerGradient)"/>
                        <!-- 半圆弧边框 -->
                        <path d="M 40 130 A 120 120 0 0 1 280 130"
                              stroke="url(#arcGradient)"
                              stroke-width="20"
                              fill="none"
                              stroke-linecap="round"/>
                    </svg>
                </div>

                <!-- 空气质量显示 -->
                <div class="air-quality-display">
                    <div class="air-quality-label">空气优</div>
                    <div class="air-quality-value">25</div>
                    <div class="air-quality-unit">车内综合空气质量</div>
                </div>

                <!-- 汽车图标 -->
                <div class="car-display">
                    <svg width="280" height="120" viewBox="0 0 280 120">
                        <defs>
                            <!-- 车身渐变 -->
                            <linearGradient id="carBodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#5FEDD8"/>
                                <stop offset="50%" style="stop-color:#4ECDC4"/>
                                <stop offset="100%" style="stop-color:#3DBDB0"/>
                            </linearGradient>
                            <!-- 阴影渐变 -->
                            <radialGradient id="shadowGradient" cx="50%" cy="90%" r="70%">
                                <stop offset="0%" style="stop-color:rgba(0,0,0,0.2)"/>
                                <stop offset="100%" style="stop-color:rgba(0,0,0,0)"/>
                            </radialGradient>
                        </defs>

                        <!-- 车底阴影 -->
                        <ellipse cx="140" cy="105" rx="100" ry="12" fill="url(#shadowGradient)"/>

                        <!-- 车身主体 -->
                        <path d="M 50 60 Q 50 40 70 40 L 210 40 Q 230 40 230 60 L 230 80 Q 230 90 220 90 L 60 90 Q 50 90 50 80 Z"
                              fill="url(#carBodyGradient)"/>

                        <!-- 车窗 -->
                        <path d="M 70 45 Q 70 42 73 42 L 120 42 Q 123 42 123 45 L 123 55 Q 123 58 120 58 L 73 58 Q 70 58 70 55 Z"
                              fill="rgba(255,255,255,0.3)"/>
                        <path d="M 157 45 Q 157 42 160 42 L 207 42 Q 210 42 210 45 L 210 55 Q 210 58 207 58 L 160 58 Q 157 58 157 55 Z"
                              fill="rgba(255,255,255,0.3)"/>

                        <!-- 车轮 -->
                        <circle cx="85" cy="85" r="18" fill="#2C3E50"/>
                        <circle cx="85" cy="85" r="12" fill="#34495E"/>
                        <circle cx="85" cy="85" r="6" fill="#7F8C8D"/>

                        <circle cx="195" cy="85" r="18" fill="#2C3E50"/>
                        <circle cx="195" cy="85" r="12" fill="#34495E"/>
                        <circle cx="195" cy="85" r="6" fill="#7F8C8D"/>

                        <!-- 车身高光 -->
                        <path d="M 60 50 Q 140 45 220 50"
                              stroke="rgba(255,255,255,0.4)"
                              stroke-width="2"
                              fill="none"/>
                    </svg>
                </div>
            </div>

            <!-- 数据指标行 -->
            <div class="metrics-row">
                <div class="metric-item">
                    <div class="metric-value pm25">014</div>
                    <div class="metric-label">PM2.5(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value formaldehyde">36</div>
                    <div class="metric-label">甲醛(μg/m³)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value negative-ions">25500</div>
                    <div class="metric-label">负氧离子(个/cm³)</div>
                </div>
            </div>

            <!-- 天气卡片 -->
            <div class="weather-card">
                <div class="weather-header">
                    <div class="weather-left">
                        <div class="weather-location">深圳市</div>
                        <div class="weather-temp">33°C</div>
                    </div>
                    <div class="weather-info">
                        空气质量：优<br>
                        湿度：52%<br>
                        能见度：30km
                    </div>
                </div>
                <div class="weather-metrics">
                    <div class="weather-metric">
                        <div class="weather-metric-label">车外PM2.5</div>
                        <div class="weather-metric-value outdoor-pm25">105</div>
                    </div>
                    <div class="weather-metric">
                        <div class="weather-metric-label">车内PM2.5</div>
                        <div class="weather-metric-value indoor-pm25">14</div>
                    </div>
                    <div class="weather-metric">
                        <div class="weather-metric-label">车内负氧离子</div>
                        <div class="weather-metric-value indoor-ions">628</div>
                    </div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-left">
                    <button class="device-button">设备运转</button>
                    <div class="device-status">正常</div>
                </div>
                <button class="power-button">⏻</button>
                <div class="control-right">
                    <div class="gear-label">档位</div>
                    <div class="gear-value">低</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
