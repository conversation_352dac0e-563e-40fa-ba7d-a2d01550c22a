<template>
  <div class="iphone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="time">9:41</div>
      <div class="status-icons">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
        <div class="wifi-icon">📶</div>
        <div class="battery-icon">🔋</div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 1. 上半部分：彩色圆弧 + 空气质量 + 汽车 + 守护天数 -->
      <div class="top-section">
        <!-- 彩色圆弧背景 -->
        <div class="arc-background">
          <svg width="350" height="200" viewBox="0 0 350 200">
            <defs>
              <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
                <stop offset="25%" style="stop-color:#22d3ee;stop-opacity:1" />
                <stop offset="50%" style="stop-color:#fbbf24;stop-opacity:1" />
                <stop offset="75%" style="stop-color:#f97316;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
              </linearGradient>
            </defs>
            <path d="M 50 150 A 125 125 0 0 1 300 150" 
                  stroke="url(#arcGradient)" 
                  stroke-width="8" 
                  fill="none" 
                  stroke-linecap="round"/>
          </svg>
        </div>

        <!-- 空气质量显示 -->
        <div class="air-quality-display">
          <div class="air-quality-label">空气优</div>
          <div class="air-quality-value">25</div>
          <div class="air-quality-unit">空气质量</div>
        </div>

        <!-- 汽车图片 -->
        <div class="car-image">
          <img src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png" alt="汽车" />
        </div>

        <!-- 守护天数 -->
        <div class="protection-days">
          <div class="protection-text">已为您健康守护 231 天</div>
          <div class="protection-status">设备 清洁健康</div>
        </div>
      </div>

      <!-- 2. 数据指标行 -->
      <div class="metrics-row">
        <div class="metric-item">
          <div class="metric-value">014</div>
          <div class="metric-label">PM2.5(μg/m³)</div>
        </div>
        <div class="metric-item">
          <div class="metric-value">36</div>
          <div class="metric-label">甲醛(μg/m³)</div>
        </div>
        <div class="metric-item">
          <div class="metric-value">25500</div>
          <div class="metric-label">负氧离子(个/cm³)</div>
        </div>
      </div>

      <!-- 3. 天气卡片 -->
      <div class="weather-card">
        <div class="weather-main">
          <div class="weather-left">
            <div class="city-name">深圳市</div>
            <div class="temperature">33°C</div>
          </div>
          <div class="weather-right">
            <div class="weather-icon">☀️</div>
            <div class="weather-info">
              <div class="weather-detail">空气质量：优</div>
              <div class="weather-detail">湿度：52%</div>
              <div class="weather-detail">能见度：30km</div>
            </div>
          </div>
        </div>
        
        <div class="weather-data">
          <div class="weather-data-item">
            <div class="data-label">车外PM2.5</div>
            <div class="data-value orange">105</div>
          </div>
          <div class="weather-data-item">
            <div class="data-label">车内PM2.5</div>
            <div class="data-value green">14</div>
          </div>
          <div class="weather-data-item">
            <div class="data-label">车内负氧离子</div>
            <div class="data-value dark-green">628</div>
          </div>
        </div>
      </div>

      <!-- 4. 设备控制面板 -->
      <div class="device-control-panel">
        <div class="control-container">
          <div class="device-status">
            <div class="status-badge status-running">运行中</div>
            <div class="status-text">正常</div>
          </div>
          
          <div class="power-button">
            <div class="power-icon power-on">⏻</div>
          </div>
          
          <div class="speed-control">
            <div class="speed-label">档位</div>
            <div class="speed-buttons">
              <button class="speed-btn">-</button>
              <span class="speed-value">低</span>
              <button class="speed-btn">+</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IphoneVueSimple',
  data() {
    return {
      deviceState: {
        isRunning: true,
        speed: '低'
      }
    }
  }
}
</script>

<style scoped>
/* iPhone 容器 */
.iphone-container {
  width: 390px;
  height: 844px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 40px;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  font-weight: 600;
  z-index: 100;
}

.time {
  font-size: 17px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal-bars {
  display: flex;
  gap: 2px;
}

.bar {
  width: 3px;
  height: 4px;
  background: white;
  border-radius: 1px;
}

.bar:nth-child(2) { height: 6px; }
.bar:nth-child(3) { height: 8px; }
.bar:nth-child(4) { height: 10px; }

/* 主要内容区域 */
.main-content {
  position: absolute;
  top: 44px;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;
}

/* 1. 上半部分 */
.top-section {
  position: relative;
  height: 280px;
  margin-bottom: 20px;
}

/* 彩色圆弧 */
.arc-background {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

/* 空气质量显示 */
.air-quality-display {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
}

.air-quality-label {
  font-size: 14px;
  margin-bottom: 5px;
}

.air-quality-value {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 5px;
}

.air-quality-unit {
  font-size: 12px;
  opacity: 0.8;
}

/* 汽车图片 */
.car-image {
  position: absolute;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
}

.car-image img {
  width: 200px;
  height: auto;
}

/* 守护天数 */
.protection-days {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
}

.protection-text {
  font-size: 14px;
  margin-bottom: 5px;
}

.protection-status {
  font-size: 12px;
  opacity: 0.8;
}

/* 2. 数据指标行 */
.metrics-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 0 10px;
}

.metric-item {
  text-align: center;
  color: white;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 11px;
  opacity: 0.8;
}

/* 3. 天气卡片 */
.weather-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.weather-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.weather-left {
  flex: 1;
}

.city-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.temperature {
  font-size: 36px;
  font-weight: bold;
  color: #333;
}

.weather-right {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.weather-icon {
  font-size: 24px;
}

.weather-info {
  text-align: right;
}

.weather-detail {
  font-size: 12px;
  color: #666;
  margin-bottom: 3px;
}

.weather-data {
  display: flex;
  justify-content: space-between;
}

.weather-data-item {
  text-align: center;
  flex: 1;
}

.data-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 5px;
}

.data-value {
  font-size: 20px;
  font-weight: bold;
}

.data-value.orange {
  color: #f97316;
}

.data-value.green {
  color: #10b981;
}

.data-value.dark-green {
  color: #15803d;
}

/* 4. 设备控制面板 */
.device-control-panel {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.control-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-status {
  text-align: center;
}

.status-badge {
  background: #10b981;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 5px;
}

.status-text {
  font-size: 11px;
  color: #666;
}

.power-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f97316;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.power-icon {
  font-size: 24px;
  color: white;
}

.speed-control {
  text-align: center;
}

.speed-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.speed-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.speed-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  color: #666;
}

.speed-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  min-width: 20px;
}
</style>
