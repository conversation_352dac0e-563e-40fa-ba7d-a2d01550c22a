import vue from "@vitejs/plugin-vue";
import tailwind from "tailwindcss";
import { defineConfig } from "vite";

// https://vite.dev/config/
export default defineConfig(() => ({
  plugins: [vue()],
  publicDir: "./static",
  base: "./",
  root: ".",
  build: {
    rollupOptions: {
      input: 'vue-index.html'
    }
  },
  css: {
    postcss: {
      plugins: [tailwind()],
    },
  },
  server: {
    port: 5173,
    open: true
  }
}));
