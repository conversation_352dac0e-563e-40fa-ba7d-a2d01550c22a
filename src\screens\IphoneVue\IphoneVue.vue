<template>
  <div class="iphone-container">
    <div class="iphone-frame">
      <!-- 状态栏 -->
      <img
        class="status-bar"
        alt="Status bar"
        src="https://c.animaapp.com/mdqqarhondzElL/img/---------.svg"
      />

      <div class="main-content">
        <!-- 背景图片 -->
        <img
          class="background-image"
          alt="Background"
          src="https://c.animaapp.com/mdqqarhondzElL/img/----.png"
        />

        <!-- 圆形渐变装饰元素 -->
        <div class="circular-container">
          <img
            class="outer-ellipse"
            alt="Outer ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-11.svg"
          />
          <img
            class="middle-ellipse"
            alt="Middle ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-12.svg"
          />
          <img
            class="inner-ellipse"
            alt="Inner ellipse"
            src="https://c.animaapp.com/mdqqarhondzElL/img/ellipse-13.svg"
          />
        </div>

        <!-- 汽车可视化 -->
        <img
          class="car-image"
          alt="Car visualization"
          src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
        />

        <!-- 空气质量中心指示器 -->
        <div class="air-quality-center">
          <div class="indicator-content">
            <div class="air-status">空气优</div>
            <div class="air-level">25</div>
          </div>
          <div class="air-description">车内综合空气质量</div>
          <img
            class="air-logo"
            alt="Air quality logo"
            src="https://c.animaapp.com/mdqqarhondzElL/img/group-13.png"
          />
        </div>

        <!-- 遮罩层 -->
        <img
          class="mask-overlay"
          alt="Mask group"
          src="https://c.animaapp.com/mdqqarhondzElL/img/mask-group.png"
        />

        <!-- 健康守护信息 -->
        <div class="health-protection">
          <span class="protection-text">已为您健康守护 </span>
          <span class="days-number">{{ protectionDays }}</span>
          <span class="protection-text"> 天</span>
        </div>

        <!-- 清洗提醒设置 -->
        <div class="cleaning-reminder">
          <div class="reminder-text">清洗提醒</div>
          <div class="reminder-status">正常</div>
        </div>

        <!-- 空气质量指标网格 -->
        <div class="metrics-container">
          <div
            v-for="(metric, index) in airQualityMetrics"
            :key="index"
            :class="['metric-item', metric.width]"
          >
            <div class="metric-content">
              <div class="metric-value">{{ metric.value }}</div>
              <div class="metric-label">{{ metric.label }}</div>
            </div>
          </div>
        </div>

        <!-- 天气信息卡片 -->
        <div class="weather-card">
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载中...</div>
          </div>

          <div v-else class="weather-content">
            <!-- 刷新按钮 -->
            <button @click="handleRefresh" class="refresh-btn">
              <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 4V9H4.58152M4.58152 9C5.24618 7.35652 6.43101 5.9604 7.96 5.05C9.48899 4.1396 11.2943 3.75 13.1152 3.94148C14.9361 4.13295 16.6618 4.89479 18.0434 6.12C19.4251 7.34521 20.3898 8.97953 20.8 10.78M4.58152 9H9M20 20V15H19.4185M19.4185 15C18.7538 16.6435 17.569 18.0396 16.04 18.95C14.511 19.8604 12.7057 20.25 10.8848 20.0585C9.06393 19.867 7.33818 19.1052 5.95655 17.88C4.57493 16.6548 3.61015 15.0205 3.2 13.22M19.4185 15H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              刷新
            </button>

            <!-- 上盒子：城市温度和环境信息 -->
            <div class="top-box">
              <div class="city-temp-section">
                <div class="city-temp-line">
                  <span class="city-name">{{ weatherData?.city || "深圳市" }}</span>
                  <span class="temp-number">{{ weatherData?.temperature || 33 }}</span>
                  <span class="temp-symbol">°C</span>
                </div>
                <div class="publish-info">
                  {{ formatDate(weatherData?.publish_time) }} {{ formatTime(weatherData?.publish_time) }}发布
                </div>
              </div>

              <div class="env-section">
                <div class="env-line">空气质量: {{ weatherData?.air_quality || "中" }}</div>
                <div class="env-line">湿度: {{ weatherData?.humidity || 52 }}%</div>
                <div class="env-line">能见度: {{ weatherData?.visibility || 30 }}km</div>
              </div>
            </div>

            <!-- 下盒子：PM2.5数据指标 -->
            <div class="bottom-box">
              <div class="data-section">
                <div class="data-label">车外PM2.5</div>
                <div class="data-value orange">{{ weatherData?.pm25_outdoor || "105" }}</div>
              </div>

              <div class="data-section">
                <div class="data-label">车内PM2.5</div>
                <div class="data-value green">{{ weatherData?.pm25_indoor || "14" }}</div>
              </div>

              <div class="data-section">
                <div class="data-label">车外负氧离子</div>
                <div class="data-value dark-green">{{ weatherData?.negative_ions || "628" }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备控制面板 -->
        <div class="device-control-panel">
          <div class="control-container">
            <div class="device-status">
              <div :class="['status-badge', deviceState.isRunning ? 'status-running' : 'status-stopped']">
                {{ deviceState.isRunning ? '运行中' : '已停止' }}
              </div>
            </div>

            <div class="power-section">
              <button
                @click="handlePowerToggle"
                :class="['power-button', deviceState.isRunning ? 'power-on' : 'power-off']"
              >
                <svg class="power-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18.36 6.64a9 9 0 1 1-12.73 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <line x1="12" y1="2" x2="12" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </button>
            </div>

            <div class="level-section">
              <div class="level-display">{{ levelText }}</div>
              <div class="level-controls">
                <button @click="handleLevelChange('down')" class="level-btn">-</button>
                <button @click="handleLevelChange('up')" class="level-btn">+</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useWeatherData } from '../../composables/useWeatherData'
import { useDeviceControl } from '../../composables/useDeviceControl'

// 使用组合式函数管理状态
const { weatherData, isLoading, fetchWeatherData, startPolling } = useWeatherData()
const { deviceState, levelText, handlePowerToggle, handleLevelChange } = useDeviceControl()

// 计算属性
const protectionDays = computed(() => 231)

const airQualityMetrics = computed(() => [
  {
    value: "014",
    label: "PM2.5(µg/m³）",
    width: "w-[73px]",
  },
  {
    value: "36",
    label: "甲醛(µg/m³）",
    width: "w-[65px]",
  },
  {
    value: "25500",
    label: "负氧离子(个/cm³）",
    width: "w-[88px]",
  },
])

// 格式化发布日期
const formatDate = (publishTime?: string) => {
  if (!publishTime) return "2025-06-09"
  return publishTime.split(' ')[0]
}

// 格式化发布时间
const formatTime = (publishTime?: string) => {
  if (!publishTime) return "15:07:03"
  return publishTime.split(' ')[1]
}

// 事件处理方法
const handleRefresh = () => {
  fetchWeatherData()
}

// 生命周期
onMounted(() => {
  fetchWeatherData()
  startPolling()
})
</script>

<style scoped>
/* 主容器 */
.iphone-container {
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  width: 100%;
}

.iphone-frame {
  background: #f5f5f5;
  width: 393px;
  height: 852px;
  position: relative;
}

.main-content {
  position: absolute;
  width: 393px;
  height: 783px;
  top: 69px;
  left: 0;
  background: #f5f5f5;
}

/* 状态栏 */
.status-bar {
  position: absolute;
  width: 375px;
  height: 44px;
  top: 0;
  left: 11px;
}

/* 背景图片 - 隐藏自然背景 */
.background-image {
  display: none;
}

/* 圆形装饰元素 */
.circular-container {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
}

.outer-ellipse {
  position: absolute;
  width: 311px;
  height: 311px;
  top: 16px;
  left: 40px;
  border-radius: 50%;
  aspect-ratio: 1;
  object-fit: cover;
}

.middle-ellipse {
  position: absolute;
  width: 231px;
  height: 231px;
  top: 56px;
  left: 50px;
  border-radius: 50%;
  aspect-ratio: 1;
  object-fit: cover;
}

.inner-ellipse {
  position: absolute;
  width: 175px;
  height: 175px;
  top: 84px;
  left: 78px;
  border-radius: 50%;
  aspect-ratio: 1;
  object-fit: cover;
}

/* 汽车图片 */
.car-image {
  position: absolute;
  width: 315px;
  height: 210px;
  top: 69px;
  left: 39px;
  object-fit: cover;
}

/* 空气质量中心 */
.air-quality-center {
  position: absolute;
  width: 75px;
  height: 96px;
  top: 49px;
  left: 166px;
}

.indicator-content {
  position: absolute;
  width: 69px;
  height: 87px;
  top: 9px;
  left: 0;
}

.air-status {
  position: absolute;
  top: 0;
  left: 4px;
  color: #454545;
  font-size: 20px;
  white-space: nowrap;
  font-weight: normal;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.air-level {
  position: absolute;
  top: 30px;
  left: 0;
  color: #454545;
  font-size: 48px;
  font-weight: bold;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.air-description {
  position: absolute;
  top: 80px;
  left: 0;
  color: #909090;
  font-size: 10px;
  white-space: nowrap;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.air-logo {
  position: absolute;
  width: 177px;
  height: 90px;
  top: 58px;
  left: 105px;
}

/* 遮罩层 */
.mask-overlay {
  position: absolute;
  width: 349px;
  height: 250px;
  top: 0;
  left: 22px;
}

/* 健康守护 */
.health-protection {
  position: absolute;
  top: 256px;
  left: 109px;
  font-size: 15px;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.protection-text {
  color: #5b5b5b;
}

.days-number {
  color: #5b5b5b;
  font-size: 19px;
}

/* 清洗提醒 */
.cleaning-reminder {
  position: absolute;
  top: 600px;
  left: 50px;
  display: flex;
  gap: 10px;
}

.reminder-text {
  color: #5b5b5b;
  font-size: 12px;
}

.reminder-status {
  color: #16a34a;
  font-size: 12px;
}

/* 指标网格 */
.metrics-container {
  position: absolute;
  width: 350px;
  height: 45px;
  top: 560px;
  left: 22px;
  display: flex;
  justify-content: space-between;
}

.metric-item {
  height: 45px;
}

.metric-content {
  position: relative;
  height: 100%;
}

.metric-value {
  position: absolute;
  top: 0;
  left: 6px;
  font-weight: normal;
  color: #494949;
  font-size: 28px;
  white-space: nowrap;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

.metric-label {
  position: absolute;
  top: 33px;
  left: 0;
  font-weight: normal;
  color: #909090;
  font-size: 10px;
  white-space: nowrap;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
}

/* 天气卡片 */
.weather-card {
  position: absolute;
  height: 140px;
  top: 400px;
  left: 23px;
  width: 345px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  font-family: 'HarmonyOS_Sans-Regular', Helvetica;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #16a34a;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #16a34a;
  font-size: 14px;
}

/* 天气内容 */
.weather-content {
  height: 100%;
  position: relative;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 刷新按钮 */
.refresh-btn {
  position: absolute;
  top: 6px;
  right: 12px;
  color: #fb923c;
  font-size: 7px;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1px;
  z-index: 10;
}

.refresh-btn:hover {
  color: #f97316;
}

.refresh-icon {
  width: 8px;
  height: 8px;
}

/* 上盒子 */
.top-box {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 12px;
}

/* 城市温度区域 */
.city-temp-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.city-temp-line {
  display: flex;
  align-items: baseline;
  gap: 6px;
}

.city-name {
  color: #15803d;
  font-size: 24px;
  font-weight: bold;
  line-height: 1.2;
}

.temp-number {
  color: #15803d;
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.temp-symbol {
  color: #15803d;
  font-size: 20px;
  font-weight: bold;
  margin-left: 2px;
}

.publish-info {
  color: #16a34a;
  font-size: 8px;
  line-height: 1.2;
  margin-top: 4px;
}

/* 环境区域 */
.env-section {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  text-align: right;
  min-width: 80px;
}

.env-line {
  color: #16a34a;
  font-size: 8px;
  line-height: 1.4;
  margin-bottom: 2px;
  white-space: nowrap;
}

/* 下盒子 */
.bottom-box {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 10px;
  height: 40px;
}

/* 数据区域 */
.data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
}

.data-label {
  color: #16a34a;
  font-size: 8px;
  line-height: 1.2;
  margin-bottom: 4px;
  text-align: center;
}

.data-value {
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
  text-align: center;
}

/* 数据颜色 */
.data-value.orange {
  color: #f97316;
}

.data-value.green {
  color: #10b981;
}

.data-value.dark-green {
  color: #15803d;
}

/* 设备控制面板 */
.device-control-panel {
  position: absolute;
  top: 650px;
  left: 23px;
  width: 345px;
  height: 80px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.control-container {
  display: flex;
  height: 100%;
  padding: 16px;
}

.device-status,
.power-section,
.level-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.status-badge {
  font-size: 12px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s;
}

.status-running {
  background: #10b981;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-stopped {
  background: #94a3b8;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.power-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
  cursor: pointer;
}

.power-on {
  background: linear-gradient(135deg, #fb923c, #f97316);
  color: white;
  border: 2px solid #fed7aa;
}

.power-off {
  background: linear-gradient(135deg, #94a3b8, #64748b);
  color: white;
  border: 2px solid #cbd5e1;
}

.power-icon {
  width: 24px;
  height: 24px;
}

.level-display {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.level-controls {
  display: flex;
  gap: 8px;
}

.level-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.level-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* 动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-content {
  animation: fadeIn 0.4s ease-out;
}
</style>
